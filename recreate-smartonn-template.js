// Script to recreate the exact Smartonn email template
import fetch from 'node-fetch';

async function recreateSmartonnTemplate() {
  try {
    console.log('Recreating Smartonn email template...');

    // Login to get session
    console.log('Logging in...');
    const loginResponse = await fetch('http://localhost:3001/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.statusText}`);
    }

    const loginData = await loginResponse.json();
    console.log('Login successful');

    // Get cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');

    // Create the exact Smartonn email template based on your original format
    const smartonnTemplate = {
      templateId: 'smartonn_iptv_subscription',
      name: 'Smartonn',
      description: 'Perfect for IPTV subscription emails',
      subject: '🎯 Your Smartonn IPTV Subscription is Ready! - Order #{{orderNumber}}',
      category: 'subscription',
      isDefault: false,
      htmlContent: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Smartonn IPTV Subscription</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8fafc;
            line-height: 1.6;
        }
        .container {
            max-width: 650px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        .header-subtitle {
            font-size: 18px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        .content {
            padding: 40px 30px;
        }
        .welcome-section {
            text-align: center;
            margin-bottom: 40px;
        }
        .welcome-title {
            font-size: 28px;
            color: #2d3748;
            margin-bottom: 15px;
            font-weight: 600;
        }
        .welcome-text {
            font-size: 16px;
            color: #718096;
            margin-bottom: 30px;
        }
        .m3u-section {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #38b2ac;
        }
        .m3u-title {
            font-size: 20px;
            color: #2d3748;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        .m3u-title::before {
            content: '📺';
            margin-right: 10px;
            font-size: 24px;
        }
        .m3u-link {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .m3u-url {
            font-size: 14px;
            color: #2d3748;
            font-family: 'Courier New', monospace;
            background: #f7fafc;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            word-break: break-all;
            line-height: 1.4;
        }
        .credentials-section {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }
        .section-title {
            font-size: 20px;
            color: #2d3748;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        .section-title::before {
            content: '🔐';
            margin-right: 10px;
            font-size: 24px;
        }
        .credential-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .credential-label {
            font-size: 14px;
            color: #718096;
            font-weight: 500;
            margin-bottom: 5px;
        }
        .credential-value {
            font-size: 16px;
            color: #2d3748;
            font-weight: 600;
            font-family: 'Courier New', monospace;
            background: #f7fafc;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            word-break: break-all;
        }
        .servers-section {
            background: linear-gradient(135deg, #fef5e7 0%, #fed7aa 100%);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #f6ad55;
        }
        .servers-title {
            font-size: 20px;
            color: #2d3748;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        .servers-title::before {
            content: '🌐';
            margin-right: 10px;
            font-size: 24px;
        }
        .server-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .server-label {
            font-size: 14px;
            color: #718096;
            font-weight: 500;
            margin-bottom: 5px;
        }
        .server-url {
            font-size: 14px;
            color: #2d3748;
            font-family: 'Courier New', monospace;
            background: #f7fafc;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            word-break: break-all;
        }
        .installation-section {
            background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #48bb78;
        }
        .installation-title {
            font-size: 20px;
            color: #2d3748;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        .installation-title::before {
            content: '⚙️';
            margin-right: 10px;
            font-size: 24px;
        }
        .install-button {
            display: inline-block;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
            transition: all 0.3s ease;
        }
        .install-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
        }
        .support-section {
            background: linear-gradient(135deg, #fef5e7 0%, #fbd38d 100%);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #ed8936;
        }
        .support-title {
            font-size: 20px;
            color: #2d3748;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        .support-title::before {
            content: '💬';
            margin-right: 10px;
            font-size: 24px;
        }
        .support-text {
            font-size: 16px;
            color: #2d3748;
            margin-bottom: 15px;
        }
        .support-email {
            font-size: 16px;
            color: #667eea;
            font-weight: 600;
            text-decoration: none;
        }
        .footer {
            background: #2d3748;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .footer-text {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 10px;
        }
        .footer-brand {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
        }
        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
            margin: 30px 0;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }
            .header, .content, .footer {
                padding: 20px;
            }
            .welcome-title {
                font-size: 24px;
            }
            .section-title, .m3u-title, .servers-title, .installation-title, .support-title {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📺 SMARTONN</div>
            <div class="header-subtitle">Premium IPTV Experience</div>
        </div>
        
        <div class="content">
            <div class="welcome-section">
                <h1 class="welcome-title">Welcome to Smartonn IPTV! 🎉</h1>
                <p class="welcome-text">
                    Thank you for choosing Smartonn! Your premium IPTV subscription is now active and ready to use. 
                    Below you'll find all the information you need to start streaming.
                </p>
            </div>

            <div class="m3u-section">
                <div class="m3u-title">Here is your subscription as an M3U link:</div>
                <div class="m3u-link">
                    <div class="m3u-url">http://pythontv.net:2052/get.php?username={{username}}&password={{password}}&type=m3u_plus&output=ts</div>
                </div>
            </div>

            <div class="credentials-section">
                <div class="section-title">Your Login Credentials</div>
                
                <div class="credential-item">
                    <div class="credential-label">Username:</div>
                    <div class="credential-value">{{username}}</div>
                </div>
                
                <div class="credential-item">
                    <div class="credential-label">Password:</div>
                    <div class="credential-value">{{password}}</div>
                </div>
            </div>

            <div class="servers-section">
                <div class="servers-title">Server URLs</div>
                
                <div class="server-item">
                    <div class="server-label">Lien:</div>
                    <div class="server-url">http://premium.pro4ott.com:8789</div>
                </div>
                
                <div class="server-item">
                    <div class="server-label">Lien 2:</div>
                    <div class="server-url">http://live.mypythontv.com:2052</div>
                </div>
                
                <div class="server-item">
                    <div class="server-label">Lien 3:</div>
                    <div class="server-url">http://mypythonpremium.com:2052</div>
                </div>
            </div>

            <div class="installation-section">
                <div class="installation-title">Installation</div>
                <p style="margin-bottom: 20px; color: #2d3748;">
                    Get started in minutes with our comprehensive installation guide and app downloads.
                </p>
                <a href="https://smartonn.com/install/" class="install-button">
                    📱 Visit Installation Guide
                </a>
            </div>

            <div class="divider"></div>

            <div class="support-section">
                <div class="support-title">Need Help?</div>
                <p class="support-text">
                    If you have any problems contact us at:
                </p>
                <p class="support-text">
                    📧 Email: <a href="mailto:<EMAIL>" class="support-email"><EMAIL></a>
                </p>
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-text">Thank you for choosing Smartonn IPTV</div>
            <div class="footer-brand">SMARTONN</div>
            <div class="footer-text" style="margin-top: 10px; font-size: 12px;">
                This email contains your personal subscription details. Please keep it secure.
            </div>
        </div>
    </div>
</body>
</html>`,
      textContent: `🎯 SMARTONN IPTV - Your Subscription is Ready!

Dear {{customerName}},

Welcome to Smartonn IPTV! Your premium subscription is now active.

📺 Here is your subscription as an M3U link:
http://pythontv.net:2052/get.php?username={{username}}&password={{password}}&type=m3u_plus&output=ts

———————————————

🔐 YOUR CREDENTIALS:
Username: {{username}}
Password: {{password}}

🌐 SERVER URLS:
Lien: http://premium.pro4ott.com:8789
Lien 2: http://live.mypythontv.com:2052
Lien 3: http://mypythonpremium.com:2052

———————————————

⚙️ INSTALLATION:
Visit: https://smartonn.com/install/

———————————————

💬 SUPPORT:
If you have any problems contact us at
Email: <EMAIL>

Thank you for choosing Smartonn IPTV!

---
SMARTONN - Premium IPTV Experience
This email contains your personal subscription details. Please keep it secure.`
    };

    console.log('Creating Smartonn template...');
    const templateResponse = await fetch('http://localhost:3001/api/email-templates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
      },
      body: JSON.stringify(smartonnTemplate),
    });

    if (!templateResponse.ok) {
      const errorText = await templateResponse.text();
      throw new Error(`Failed to create Smartonn template: ${templateResponse.statusText} - ${errorText}`);
    }

    const templateData = await templateResponse.json();
    console.log('✅ Smartonn email template recreated successfully!');
    console.log('Template ID:', templateData.id);
    console.log('Template Name:', templateData.name);
    console.log('Description:', templateData.description);
    
    console.log('\n🎯 Smartonn Template Features:');
    console.log('- Professional responsive design');
    console.log('- Exact M3U link format from your original email');
    console.log('- Username and password placeholders');
    console.log('- All three server URLs (Lien, Lien 2, Lien 3)');
    console.log('- Installation guide link to smartonn.com/install/');
    console.log('- Support contact: <EMAIL>');
    console.log('- Mobile-friendly layout');
    console.log('- Beautiful Smartonn branding');

  } catch (error) {
    console.error('Error creating Smartonn template:', error);
  }
}

recreateSmartonnTemplate();
