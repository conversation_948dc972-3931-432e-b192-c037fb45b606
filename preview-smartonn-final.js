// <PERSON>ript to preview the recreated Smartonn template
import fetch from 'node-fetch';

async function previewSmartonnTemplate() {
  try {
    console.log('Previewing recreated Smartonn template...');

    // Login to get session
    const loginResponse = await fetch('http://localhost:3001/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.statusText}`);
    }

    // Get cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');

    // Preview the template with test data
    const testData = {
      customerName: '<PERSON>',
      orderNumber: 'ORD-12345',
      username: 'grrkennn001',
      password: 'kefdgdgfm',
      productName: 'IPTV Premium Subscription',
      amount: '19.99',
      country: 'United States',
      appType: 'IPTV Smarters Pro'
    };

    const previewResponse = await fetch('http://localhost:3001/api/email-templates/preview', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
      },
      body: JSON.stringify({
        templateId: 9, // Smartonn template ID
        testData: testData
      }),
    });

    if (!previewResponse.ok) {
      throw new Error(`Failed to preview template: ${previewResponse.statusText}`);
    }

    const previewData = await previewResponse.json();
    
    console.log('✅ Smartonn template preview generated successfully!');
    console.log('\n📧 Subject:', previewData.subject);
    console.log('\n📝 HTML Content Length:', previewData.htmlContent.length);
    console.log('\n📄 Text Content Length:', previewData.textContent.length);
    
    // Check if placeholders were replaced
    const hasPlaceholders = previewData.htmlContent.includes('{{');
    console.log('\n🔄 Placeholders replaced:', !hasPlaceholders);
    
    // Show key content snippets
    console.log('\n🔍 Key Content Verification:');
    console.log('- M3U Link:', previewData.htmlContent.includes('http://pythontv.net:2052/get.php?username=grrkennn001&password=kefdgdgfm') ? '✅' : '❌');
    console.log('- Username:', previewData.htmlContent.includes('grrkennn001') ? '✅' : '❌');
    console.log('- Password:', previewData.htmlContent.includes('kefdgdgfm') ? '✅' : '❌');
    console.log('- Server URLs:', previewData.htmlContent.includes('http://premium.pro4ott.com:8789') ? '✅' : '❌');
    console.log('- Installation Link:', previewData.htmlContent.includes('https://smartonn.com/install/') ? '✅' : '❌');
    console.log('- Support Email:', previewData.htmlContent.includes('<EMAIL>') ? '✅' : '❌');

    // Save a preview HTML file for testing
    const fs = await import('fs');
    fs.writeFileSync('smartonn-final-preview.html', previewData.htmlContent);
    console.log('\n💾 Preview saved to: smartonn-final-preview.html');
    console.log('You can open this file in a browser to see the final Smartonn template.');

  } catch (error) {
    console.error('Error previewing template:', error);
  }
}

previewSmartonnTemplate();
