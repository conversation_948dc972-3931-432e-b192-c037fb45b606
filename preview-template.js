// <PERSON>ript to preview the Smartonn template
import fetch from 'node-fetch';

async function previewTemplate() {
  try {
    console.log('Previewing Smartonn template...');

    // Login to get session
    const loginResponse = await fetch('http://localhost:3001/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.statusText}`);
    }

    // Get cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');

    // Preview the template with test data
    const testData = {
      customerName: '<PERSON>',
      orderNumber: 'ORD-12345',
      username: 'grrkennn001',
      password: 'kefdgdgfm',
      productName: 'IPTV Premium Subscription',
      amount: '19.99',
      country: 'United States',
      appType: 'IPTV Smarters Pro'
    };

    const previewResponse = await fetch('http://localhost:3001/api/email-templates/preview', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
      },
      body: JSON.stringify({
        templateId: 9, // Smartonn template ID
        testData: testData
      }),
    });

    if (!previewResponse.ok) {
      throw new Error(`Failed to preview template: ${previewResponse.statusText}`);
    }

    const previewData = await previewResponse.json();
    
    console.log('✅ Template preview generated successfully!');
    console.log('\n📧 Subject:', previewData.subject);
    console.log('\n📝 HTML Content Length:', previewData.htmlContent.length);
    console.log('\n📄 Text Content Length:', previewData.textContent.length);
    
    // Show a snippet of the HTML content
    const htmlSnippet = previewData.htmlContent.substring(0, 500);
    console.log('\n🔍 HTML Content Preview (first 500 chars):');
    console.log(htmlSnippet + '...');
    
    // Check if placeholders were replaced
    const hasPlaceholders = previewData.htmlContent.includes('{{');
    console.log('\n🔄 Placeholders replaced:', !hasPlaceholders);
    
    if (hasPlaceholders) {
      console.log('⚠️  Some placeholders were not replaced. Check the test data.');
    }

    // Save a preview HTML file for testing
    const fs = await import('fs');
    fs.writeFileSync('smartonn-preview.html', previewData.htmlContent);
    console.log('\n💾 Preview saved to: smartonn-preview.html');
    console.log('You can open this file in a browser to see how the email looks.');

  } catch (error) {
    console.error('Error previewing template:', error);
  }
}

previewTemplate();
