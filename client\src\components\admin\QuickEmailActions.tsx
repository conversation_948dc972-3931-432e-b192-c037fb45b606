import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Send, RefreshCw } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AdvancedEmailEditor from '@/components/admin/AdvancedEmailEditor';

interface QuickEmailActionsProps {
  email: string;
  lastSmtpProvider?: string;
}

export default function QuickEmailActions({ email, lastSmtpProvider }: QuickEmailActionsProps) {
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [emailSubject, setEmailSubject] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [isHtml, setIsHtml] = useState(true);
  const [isPreview, setIsPreview] = useState(false);
  const [isSending, setIsSending] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch email templates from API
  const { data: emailTemplates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['/api/email-templates'],
    queryFn: () => apiRequest('/api/email-templates', 'GET'),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Hardcoded SMTP config for now
  const emailConfig = {
    providers: [
      { id: 'default', name: 'Default SMTP', isDefault: true }
    ]
  };

  // Mutation to send email
  const sendEmailMutation = useMutation({
    mutationFn: (data: any) => {
      console.log('Sending email:', data);
      // For now, just simulate success
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true, message: 'Email sent successfully' });
        }, 1000);
      });
      // In production, this would call the API
      // return apiRequest('/api/send-email', 'POST', data);
    },
    onSuccess: () => {
      setIsEmailDialogOpen(false);
      setEmailSubject('');
      setEmailContent('');
      setSelectedTemplate(null);
      setIsSending(false);

      toast({
        title: 'Success',
        description: 'Email sent successfully',
      });

      // Refresh allowed emails list to get updated last subject and SMTP provider
      queryClient.invalidateQueries({ queryKey: ['/api/allowed-emails'] });
    },
    onError: (error: any) => {
      setIsSending(false);
      toast({
        title: 'Error',
        description: error.message || 'Failed to send email',
        variant: 'destructive',
      });
    }
  });

  // Handle template selection
  const handleTemplateSelect = async (template: any) => {
    setSelectedTemplate(template);

    if (template) {
      // Replace placeholders with customer data
      let subject = template.subject;
      let content = template.htmlContent || template.content;

      // Replace basic placeholders
      const currentDate = new Date();
      const placeholders = {
        customerName: email.split('@')[0], // Use part before @ as name
        customerEmail: email,
        currentYear: currentDate.getFullYear().toString(),
        date: currentDate.toLocaleDateString(),
        orderNumber: `ORD-${Math.floor(Math.random() * 10000)}`, // Random order number for demo
        username: `user_${Math.floor(Math.random() * 1000)}`, // Demo username
        password: `pass_${Math.floor(Math.random() * 1000)}`, // Demo password
        productName: 'IPTV Premium Subscription',
        amount: '$19.99',
        country: 'United States',
        appType: 'IPTV Smarters Pro'
      };

      Object.entries(placeholders).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        subject = subject.replace(regex, value as string);
        content = content.replace(regex, value as string);
      });

      setEmailSubject(subject);
      setEmailContent(content);
      setIsHtml(Boolean(template.htmlContent));

      // Open the email dialog
      setIsEmailDialogOpen(true);
    }
  };

  // Handle sending email
  const handleSendEmail = () => {
    if (!email || !emailSubject || !emailContent) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    setIsSending(true);

    // Find the SMTP provider to use (use the last one used for this email if available)
    let smtpProviderId = lastSmtpProvider;

    // If no last provider, use the default one
    if (!smtpProviderId && emailConfig && emailConfig.providers) {
      const defaultProvider = emailConfig.providers.find((p: any) => p.isDefault);
      if (defaultProvider) {
        smtpProviderId = defaultProvider.id;
      }
    }

    sendEmailMutation.mutate({
      to: email,
      subject: emailSubject,
      content: emailContent,
      smtpProviderId,
      addToAllowedEmails: true,
      isHtml
    });
  };

  // Get template categories
  const getTemplateCategories = () => {
    if (!emailTemplates) return [];
    const categories = new Set<string>();
    emailTemplates.forEach((template: any) => {
      if (template.category) {
        categories.add(template.category);
      }
    });
    return Array.from(categories);
  };

  // Group templates by category
  const templatesByCategory = getTemplateCategories().reduce((acc, category) => {
    acc[category] = emailTemplates?.filter((template: any) => template.category === category) || [];
    return acc;
  }, {} as Record<string, any[]>);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <Send className="h-4 w-4 mr-1" />
            Quick Email
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Send Email</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {isLoadingTemplates ? (
            <DropdownMenuItem disabled>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Loading templates...
            </DropdownMenuItem>
          ) : (
            <>
              {emailTemplates && emailTemplates.length > 0 ? (
                <>
                  {emailTemplates.map((template: any) => (
                    <DropdownMenuItem
                      key={template.id}
                      onClick={() => handleTemplateSelect(template)}
                    >
                      {template.name}
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                </>
              ) : (
                <DropdownMenuItem disabled>
                  No templates available
                </DropdownMenuItem>
              )}

              {/* Custom Email */}
              <DropdownMenuItem onClick={() => {
                setSelectedTemplate(null);
                setEmailSubject('');
                setEmailContent('');
                setIsHtml(true);
                setIsEmailDialogOpen(true);
              }}>
                Custom Email...
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Email Dialog */}
      <Dialog open={isEmailDialogOpen} onOpenChange={setIsEmailDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {selectedTemplate ? 'Send Template Email' : 'Send Custom Email'}
            </DialogTitle>
            <DialogDescription>
              Send an email to {email}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email-subject">Subject</Label>
              <Input
                id="email-subject"
                value={emailSubject}
                onChange={(e) => setEmailSubject(e.target.value)}
                placeholder="Enter email subject"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email-content">Content</Label>
              <Tabs defaultValue="edit" value={isPreview ? 'preview' : 'edit'} onValueChange={(value) => setIsPreview(value === 'preview')}>
                <TabsList>
                  <TabsTrigger value="edit">Edit</TabsTrigger>
                  <TabsTrigger value="preview">Preview</TabsTrigger>
                </TabsList>
                <TabsContent value="edit">
                  {isHtml ? (
                    <AdvancedEmailEditor
                      initialHtmlValue={emailContent}
                      onHtmlChange={setEmailContent}
                      height={400}
                      showPreview={false}
                      showSubject={false}
                    />
                  ) : (
                    <Textarea
                      id="email-content"
                      value={emailContent}
                      onChange={(e) => setEmailContent(e.target.value)}
                      placeholder="Enter email content"
                      className="min-h-[300px]"
                    />
                  )}
                </TabsContent>
                <TabsContent value="preview">
                  <div className="border rounded-md p-4 min-h-[300px] bg-white">
                    {isHtml ? (
                      <div dangerouslySetInnerHTML={{ __html: emailContent }} />
                    ) : (
                      <pre className="whitespace-pre-wrap">{emailContent}</pre>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="is-html"
                checked={isHtml}
                onCheckedChange={(checked) => setIsHtml(checked as boolean)}
              />
              <Label htmlFor="is-html">Send as HTML</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEmailDialogOpen(false)} disabled={isSending}>
              Cancel
            </Button>
            <Button onClick={handleSendEmail} disabled={isSending}>
              {isSending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Email
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
