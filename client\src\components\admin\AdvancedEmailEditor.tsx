import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Bold,
  Italic,
  Underline,
  Link,
  Image,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  Monitor,
  Tablet,
  Smartphone,
  Upload,
} from 'lucide-react';

interface AdvancedEmailEditorProps {
  initialHtmlValue?: string;
  initialSubject?: string;
  onHtmlChange?: (html: string) => void;
  onSubjectChange?: (subject: string) => void;
  height?: number;
  showPreview?: boolean;
  showSubject?: boolean;
}

interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  html: string;
}

const AdvancedEmailEditor: React.FC<AdvancedEmailEditorProps> = ({
  initialHtmlValue = '',
  initialSubject = '',
  onHtmlChange,
  onSubjectChange,
  height = 400,
  showPreview = true,
  showSubject = true,
}) => {
  const [htmlContent, setHtmlContent] = useState(initialHtmlValue);
  const [subject, setSubject] = useState(initialSubject);
  const [activeTab, setActiveTab] = useState('visual');
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [linkText, setLinkText] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Built-in email templates - Simple and Gmail compatible
  const emailTemplates: EmailTemplate[] = [
    {
      id: 'welcome',
      name: 'Welcome Email',
      description: 'Simple welcome message for new users',
      html: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Welcome</title>
</head>
<body style="margin: 0; padding: 20px; font-family: Arial, sans-serif; background-color: #f5f5f5;">
    <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border: 1px solid #dddddd;">
        <tr>
            <td style="background-color: #4a5568; color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 24px;">Welcome!</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #2d3748; margin: 0 0 20px 0;">Hello {{customerName}}!</h2>
                <p style="color: #4a5568; margin: 0 0 20px 0; line-height: 1.6;">Welcome to our service. We're excited to have you on board!</p>
                <p style="color: #4a5568; margin: 0; line-height: 1.6;">If you have any questions, feel free to contact us.</p>
            </td>
        </tr>
    </table>
</body>
</html>`
    },
    {
      id: 'smartonn',
      name: 'Smartonn',
      description: 'Simple and clean IPTV subscription email - Gmail compatible',
      html: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Smartonn IPTV Subscription</title>
</head>
<body style="margin: 0; padding: 20px; font-family: Arial, sans-serif; background-color: #f5f5f5;">
    <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border: 1px solid #dddddd;">
        <!-- Header -->
        <tr>
            <td style="background-color: #4a5568; color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 24px; font-weight: bold;">📺 SMARTONN</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px;">Premium IPTV Experience</p>
            </td>
        </tr>

        <!-- Content -->
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #2d3748; margin: 0 0 20px 0; font-size: 20px;">Welcome to Smartonn IPTV!</h2>
                <p style="color: #4a5568; margin: 0 0 30px 0; line-height: 1.6;">Thank you for choosing Smartonn! Your premium IPTV subscription is now active and ready to use.</p>

                <!-- M3U Link Section -->
                <div style="background-color: #f7fafc; border: 1px solid #e2e8f0; padding: 20px; margin-bottom: 20px;">
                    <p style="margin: 0 0 10px 0; font-weight: bold; color: #2d3748;">Here is your subscription as an M3U link:</p>
                    <p style="margin: 0; font-family: monospace; font-size: 14px; color: #2d3748; word-break: break-all; background-color: #ffffff; padding: 10px; border: 1px solid #e2e8f0;">http://pythontv.net:2052/get.php?username={{username}}&password={{password}}&type=m3u_plus&output=ts</p>
                </div>

                <!-- Divider -->
                <p style="text-align: center; margin: 30px 0; color: #a0aec0; font-size: 18px;">———————————————</p>

                <!-- Credentials Section -->
                <div style="background-color: #f7fafc; border: 1px solid #e2e8f0; padding: 20px; margin-bottom: 20px;">
                    <table width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="padding: 10px 0;">
                                <strong style="color: #2d3748;">Username:</strong>
                                <span style="font-family: monospace; background-color: #ffffff; padding: 5px 10px; border: 1px solid #e2e8f0; margin-left: 10px;">{{username}}</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 10px 0;">
                                <strong style="color: #2d3748;">Password:</strong>
                                <span style="font-family: monospace; background-color: #ffffff; padding: 5px 10px; border: 1px solid #e2e8f0; margin-left: 10px;">{{password}}</span>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Server URLs -->
                <div style="background-color: #f7fafc; border: 1px solid #e2e8f0; padding: 20px; margin-bottom: 20px;">
                    <table width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="padding: 8px 0;">
                                <strong style="color: #2d3748;">Lien:</strong>
                                <span style="font-family: monospace; font-size: 14px; color: #2d3748; margin-left: 10px;">http://premium.pro4ott.com:8789</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0;">
                                <strong style="color: #2d3748;">Lien 2:</strong>
                                <span style="font-family: monospace; font-size: 14px; color: #2d3748; margin-left: 10px;">http://live.mypythontv.com:2052</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0;">
                                <strong style="color: #2d3748;">Lien 3:</strong>
                                <span style="font-family: monospace; font-size: 14px; color: #2d3748; margin-left: 10px;">http://mypythonpremium.com:2052</span>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Divider -->
                <p style="text-align: center; margin: 30px 0; color: #a0aec0; font-size: 18px;">———————————————</p>

                <!-- Installation Section -->
                <div style="background-color: #f0fff4; border: 1px solid #c6f6d5; padding: 20px; margin-bottom: 20px;">
                    <p style="margin: 0 0 15px 0; font-weight: bold; color: #2d3748;">Installation:</p>
                    <p style="margin: 0;"><a href="https://smartonn.com/install/" style="color: #3182ce; text-decoration: none;">https://smartonn.com/install/</a></p>
                </div>

                <!-- Divider -->
                <p style="text-align: center; margin: 30px 0; color: #a0aec0; font-size: 18px;">———————————————</p>

                <!-- Support Section -->
                <div style="background-color: #fffaf0; border: 1px solid #fbd38d; padding: 20px; margin-bottom: 20px;">
                    <p style="margin: 0 0 10px 0; color: #2d3748;">If you have any problems contact us at</p>
                    <p style="margin: 0; color: #2d3748;"><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #3182ce; text-decoration: none;"><EMAIL></a></p>
                </div>
            </td>
        </tr>

        <!-- Footer -->
        <tr>
            <td style="background-color: #2d3748; color: white; padding: 20px; text-align: center;">
                <p style="margin: 0 0 10px 0; font-size: 14px;">Thank you for choosing Smartonn IPTV</p>
                <p style="margin: 0; font-weight: bold; color: #a0aec0;">SMARTONN</p>
            </td>
        </tr>
    </table>
</body>
</html>`
    }
  ];

  useEffect(() => {
    setHtmlContent(initialHtmlValue);
  }, [initialHtmlValue]);

  useEffect(() => {
    setSubject(initialSubject);
  }, [initialSubject]);

  const handleHtmlChange = (newHtml: string) => {
    setHtmlContent(newHtml);
    if (onHtmlChange) {
      onHtmlChange(newHtml);
    }
  };

  const handleSubjectChange = (newSubject: string) => {
    setSubject(newSubject);
    if (onSubjectChange) {
      onSubjectChange(newSubject);
    }
  };

  const execCommand = (command: string, value: string = '') => {
    if (editorRef.current) {
      document.execCommand(command, false, value);
      const newContent = editorRef.current.innerHTML;
      handleHtmlChange(newContent);
    }
  };

  const insertImage = () => {
    if (imageUrl) {
      const imgHtml = `<img src="${imageUrl}" alt="${imageAlt}" style="max-width: 100%; height: auto;" />`;
      execCommand('insertHTML', imgHtml);
      setImageUrl('');
      setImageAlt('');
      setIsImageDialogOpen(false);
    }
  };

  const insertLink = () => {
    if (linkUrl && linkText) {
      const linkHtml = `<a href="${linkUrl}" style="color: #3182ce; text-decoration: none;">${linkText}</a>`;
      execCommand('insertHTML', linkHtml);
      setLinkText('');
      setLinkUrl('');
      setIsLinkDialogOpen(false);
    }
  };

  const insertPlaceholder = (placeholder: string) => {
    execCommand('insertHTML', `{{${placeholder}}}`);
  };

  const loadTemplate = (template: EmailTemplate) => {
    handleHtmlChange(template.html);

    // Set default subject based on template
    if (template.id === 'smartonn' && onSubjectChange) {
      onSubjectChange('🎯 Your Smartonn IPTV Subscription is Ready! - Order #{{orderNumber}}');
    }

    toast({
      title: "Template Loaded",
      description: `${template.name} template has been loaded.`,
    });
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          setImageUrl(result);
          setIsImageDialogOpen(true);
        };
        reader.readAsDataURL(file);
      } else {
        toast({
          title: "Invalid File",
          description: "Please select an image file.",
          variant: "destructive",
        });
      }
    }
  };

  const getPreviewWidth = () => {
    switch (previewMode) {
      case 'mobile': return '375px';
      case 'tablet': return '768px';
      default: return '100%';
    }
  };

  return (
    <div className="space-y-4">
      {showSubject && (
        <div>
          <Label htmlFor="email-subject">Subject</Label>
          <Input
            id="email-subject"
            value={subject}
            onChange={(e) => handleSubjectChange(e.target.value)}
            placeholder="Email subject..."
          />
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="visual">Visual Editor</TabsTrigger>
            <TabsTrigger value="code">HTML Code</TabsTrigger>
            {showPreview && <TabsTrigger value="preview">Preview</TabsTrigger>}
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>

          {activeTab === 'preview' && (
            <div className="flex gap-2">
              <Button
                variant={previewMode === 'desktop' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('desktop')}
              >
                <Monitor className="h-4 w-4" />
              </Button>
              <Button
                variant={previewMode === 'tablet' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('tablet')}
              >
                <Tablet className="h-4 w-4" />
              </Button>
              <Button
                variant={previewMode === 'mobile' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('mobile')}
              >
                <Smartphone className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        <TabsContent value="visual">
          {/* Toolbar */}
          <div className="flex flex-wrap gap-1 p-2 border-b bg-muted/50">
            <Button variant="ghost" size="sm" onClick={() => execCommand('bold')}>
              <Bold className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('italic')}>
              <Italic className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('underline')}>
              <Underline className="h-4 w-4" />
            </Button>

            <div className="w-px h-6 bg-border mx-1" />

            <Button variant="ghost" size="sm" onClick={() => execCommand('formatBlock', 'h1')}>
              <Heading1 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('formatBlock', 'h2')}>
              <Heading2 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('formatBlock', 'h3')}>
              <Heading3 className="h-4 w-4" />
            </Button>

            <div className="w-px h-6 bg-border mx-1" />

            <Button variant="ghost" size="sm" onClick={() => execCommand('justifyLeft')}>
              <AlignLeft className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('justifyCenter')}>
              <AlignCenter className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('justifyRight')}>
              <AlignRight className="h-4 w-4" />
            </Button>

            <div className="w-px h-6 bg-border mx-1" />

            <Button variant="ghost" size="sm" onClick={() => execCommand('insertUnorderedList')}>
              <List className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('insertOrderedList')}>
              <ListOrdered className="h-4 w-4" />
            </Button>

            <div className="w-px h-6 bg-border mx-1" />

            <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Link className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Insert Link</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="link-text">Link Text</Label>
                    <Input
                      id="link-text"
                      value={linkText}
                      onChange={(e) => setLinkText(e.target.value)}
                      placeholder="Enter link text"
                    />
                  </div>
                  <div>
                    <Label htmlFor="link-url">URL</Label>
                    <Input
                      id="link-url"
                      value={linkUrl}
                      onChange={(e) => setLinkUrl(e.target.value)}
                      placeholder="https://example.com"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsLinkDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={insertLink}>Insert Link</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Image className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Insert Image</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="image-upload">Upload Image</Label>
                    <div className="flex gap-2">
                      <Input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                      <Button
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Choose File
                      </Button>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="image-url">Or Image URL</Label>
                    <Input
                      id="image-url"
                      value={imageUrl}
                      onChange={(e) => setImageUrl(e.target.value)}
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>
                  <div>
                    <Label htmlFor="image-alt">Alt Text</Label>
                    <Input
                      id="image-alt"
                      value={imageAlt}
                      onChange={(e) => setImageAlt(e.target.value)}
                      placeholder="Image description"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsImageDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={insertImage}>Insert Image</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <div className="w-px h-6 bg-border mx-1" />

            <Select onValueChange={(value) => insertPlaceholder(value)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Variables" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="customerName">Customer Name</SelectItem>
                <SelectItem value="customerEmail">Customer Email</SelectItem>
                <SelectItem value="username">Username</SelectItem>
                <SelectItem value="password">Password</SelectItem>
                <SelectItem value="orderNumber">Order Number</SelectItem>
                <SelectItem value="productName">Product Name</SelectItem>
                <SelectItem value="amount">Amount</SelectItem>
                <SelectItem value="country">Country</SelectItem>
                <SelectItem value="appType">App Type</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Editor */}
          <div
            ref={editorRef}
            contentEditable
            className="p-4 focus:outline-none border"
            style={{ minHeight: `${height}px` }}
            onInput={(e) => handleHtmlChange((e.target as HTMLDivElement).innerHTML)}
            dangerouslySetInnerHTML={{ __html: htmlContent }}
          />
        </TabsContent>

        <TabsContent value="code">
          <Textarea
            value={htmlContent}
            onChange={(e) => handleHtmlChange(e.target.value)}
            className="font-mono text-sm"
            style={{ minHeight: `${height}px` }}
            placeholder="Enter HTML code here..."
          />
        </TabsContent>

        {showPreview && (
          <TabsContent value="preview">
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-muted p-2 text-sm text-center">
                Preview ({previewMode})
              </div>
              <div className="p-4 bg-gray-100 overflow-auto">
                <div
                  style={{
                    width: getPreviewWidth(),
                    margin: '0 auto',
                    backgroundColor: 'white',
                    minHeight: `${height}px`
                  }}
                  dangerouslySetInnerHTML={{ __html: htmlContent }}
                />
              </div>
            </div>
          </TabsContent>
        )}

        <TabsContent value="templates">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {emailTemplates.map((template) => (
              <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <p className="text-sm text-muted-foreground">{template.description}</p>
                </CardHeader>
                <CardContent>
                  <Button onClick={() => loadTemplate(template)} className="w-full">
                    Use Template
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedEmailEditor;