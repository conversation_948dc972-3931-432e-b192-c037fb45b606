import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight,
  List, ListOrdered, Link, Image, Code, Eye, Save, Upload,
  Type, Heading1, Heading2, Heading3, Palette, Layout,
  FileImage, Monitor, Smartphone, Tablet, Download
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';

interface AdvancedEmailEditorProps {
  initialHtmlValue: string;
  initialSubject?: string;
  onHtmlChange: (content: string) => void;
  onSubjectChange?: (subject: string) => void;
  height?: number;
  showPreview?: boolean;
  showSubject?: boolean;
}

interface EmailTemplate {
  id: string;
  name: string;
  html: string;
  description: string;
}

const AdvancedEmailEditor: React.FC<AdvancedEmailEditorProps> = ({
  initialHtmlValue,
  initialSubject = '',
  onHtmlChange,
  onSubjectChange,
  height = 500,
  showPreview = true,
  showSubject = true,
}) => {
  const [htmlContent, setHtmlContent] = useState(initialHtmlValue);
  const [subject, setSubject] = useState(initialSubject);
  const [activeTab, setActiveTab] = useState<string>('visual');
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const [linkText, setLinkText] = useState('');
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Email template presets
  const emailTemplates: EmailTemplate[] = [
    {
      id: 'blank',
      name: 'Blank Template',
      description: 'Start with a clean slate',
      html: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Your Email Content Here</h1>
        <p>Start editing this template...</p>
    </div>
</body>
</html>`
    },
    {
      id: 'modern',
      name: 'Modern Business',
      description: 'Clean and professional design',
      html: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Email</title>
    <style>
        body { margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 20px; text-align: center; }
        .content { padding: 40px 20px; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; }
        .btn { display: inline-block; background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome!</h1>
            <p>Professional email template</p>
        </div>
        <div class="content">
            <h2>Hello {{customerName}},</h2>
            <p>This is a modern, responsive email template that looks great on all devices.</p>
            <a href="#" class="btn">Call to Action</a>
        </div>
        <div class="footer">
            <p>&copy; 2024 Your Company. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`
    },
    {
      id: 'iptv',
      name: 'IPTV Service',
      description: 'Perfect for IPTV subscription emails',
      html: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPTV Subscription</title>
    <style>
        body { margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8fafc; }
        .container { max-width: 650px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); border-radius: 12px; overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 30px; text-align: center; }
        .content { padding: 40px 30px; }
        .credentials-section { background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-radius: 12px; padding: 30px; margin-bottom: 30px; border-left: 5px solid #667eea; }
        .credential-item { background: white; border-radius: 8px; padding: 15px; margin-bottom: 15px; border: 1px solid #e2e8f0; }
        .credential-label { font-size: 14px; color: #718096; font-weight: 500; margin-bottom: 5px; }
        .credential-value { font-size: 16px; color: #2d3748; font-weight: 600; font-family: 'Courier New', monospace; background: #f7fafc; padding: 8px 12px; border-radius: 6px; }
        .footer { background: #2d3748; color: white; padding: 30px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📺 Your IPTV Service</h1>
            <p>Premium Streaming Experience</p>
        </div>
        <div class="content">
            <h2>Welcome {{customerName}}!</h2>
            <p>Your IPTV subscription is now active. Here are your credentials:</p>
            
            <div class="credentials-section">
                <h3>🔐 Login Credentials</h3>
                <div class="credential-item">
                    <div class="credential-label">Username:</div>
                    <div class="credential-value">{{username}}</div>
                </div>
                <div class="credential-item">
                    <div class="credential-label">Password:</div>
                    <div class="credential-value">{{password}}</div>
                </div>
            </div>
            
            <p><strong>M3U Link:</strong><br>
            <code>http://example.com/get.php?username={{username}}&password={{password}}&type=m3u_plus</code></p>
        </div>
        <div class="footer">
            <p>Thank you for choosing our IPTV service!</p>
        </div>
    </div>
</body>
</html>`
    }
  ];

  useEffect(() => {
    setHtmlContent(initialHtmlValue);
  }, [initialHtmlValue]);

  useEffect(() => {
    setSubject(initialSubject);
  }, [initialSubject]);

  const handleHtmlChange = (newContent: string) => {
    setHtmlContent(newContent);
    onHtmlChange(newContent);
  };

  const handleSubjectChange = (newSubject: string) => {
    setSubject(newSubject);
    if (onSubjectChange) {
      onSubjectChange(newSubject);
    }
  };

  const execCommand = (command: string, value: string = '') => {
    if (editorRef.current) {
      document.execCommand(command, false, value);
      const newContent = editorRef.current.innerHTML;
      handleHtmlChange(newContent);
    }
  };

  const insertImage = () => {
    if (imageUrl) {
      const imgHtml = `<img src="${imageUrl}" alt="${imageAlt}" style="max-width: 100%; height: auto;" />`;
      execCommand('insertHTML', imgHtml);
      setImageUrl('');
      setImageAlt('');
      setIsImageDialogOpen(false);
    }
  };

  const insertLink = () => {
    if (linkUrl && linkText) {
      const linkHtml = `<a href="${linkUrl}" style="color: #007bff; text-decoration: none;">${linkText}</a>`;
      execCommand('insertHTML', linkHtml);
      setLinkUrl('');
      setLinkText('');
      setIsLinkDialogOpen(false);
    }
  };

  const insertPlaceholder = (placeholder: string) => {
    execCommand('insertHTML', `{{${placeholder}}}`);
  };

  const loadTemplate = (template: EmailTemplate) => {
    handleHtmlChange(template.html);
    toast({
      title: "Template Loaded",
      description: `${template.name} template has been loaded.`,
    });
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          setImageUrl(result);
          setIsImageDialogOpen(true);
        };
        reader.readAsDataURL(file);
      } else {
        toast({
          title: "Invalid File",
          description: "Please select an image file.",
          variant: "destructive",
        });
      }
    }
  };

  const getPreviewWidth = () => {
    switch (previewMode) {
      case 'mobile': return '375px';
      case 'tablet': return '768px';
      default: return '100%';
    }
  };

  return (
    <div className="space-y-4">
      {showSubject && (
        <div>
          <Label htmlFor="email-subject">Subject</Label>
          <Input
            id="email-subject"
            value={subject}
            onChange={(e) => handleSubjectChange(e.target.value)}
            placeholder="Email subject..."
          />
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="visual">Visual Editor</TabsTrigger>
            <TabsTrigger value="code">HTML Code</TabsTrigger>
            {showPreview && <TabsTrigger value="preview">Preview</TabsTrigger>}
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>
          
          {activeTab === 'preview' && (
            <div className="flex gap-2">
              <Button
                variant={previewMode === 'desktop' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('desktop')}
              >
                <Monitor className="h-4 w-4" />
              </Button>
              <Button
                variant={previewMode === 'tablet' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('tablet')}
              >
                <Tablet className="h-4 w-4" />
              </Button>
              <Button
                variant={previewMode === 'mobile' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('mobile')}
              >
                <Smartphone className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        <TabsContent value="visual" className="space-y-4">
          {/* Toolbar */}
          <div className="flex flex-wrap gap-1 p-2 border rounded-md bg-muted/50">
            <Button variant="ghost" size="sm" onClick={() => execCommand('bold')}>
              <Bold className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('italic')}>
              <Italic className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('underline')}>
              <Underline className="h-4 w-4" />
            </Button>
            
            <div className="w-px h-6 bg-border mx-1" />
            
            <Button variant="ghost" size="sm" onClick={() => execCommand('formatBlock', 'h1')}>
              <Heading1 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('formatBlock', 'h2')}>
              <Heading2 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('formatBlock', 'h3')}>
              <Heading3 className="h-4 w-4" />
            </Button>
            
            <div className="w-px h-6 bg-border mx-1" />
            
            <Button variant="ghost" size="sm" onClick={() => execCommand('justifyLeft')}>
              <AlignLeft className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('justifyCenter')}>
              <AlignCenter className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('justifyRight')}>
              <AlignRight className="h-4 w-4" />
            </Button>
            
            <div className="w-px h-6 bg-border mx-1" />
            
            <Button variant="ghost" size="sm" onClick={() => execCommand('insertUnorderedList')}>
              <List className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => execCommand('insertOrderedList')}>
              <ListOrdered className="h-4 w-4" />
            </Button>
            
            <div className="w-px h-6 bg-border mx-1" />
            
            <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Link className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Insert Link</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="link-text">Link Text</Label>
                    <Input
                      id="link-text"
                      value={linkText}
                      onChange={(e) => setLinkText(e.target.value)}
                      placeholder="Click here"
                    />
                  </div>
                  <div>
                    <Label htmlFor="link-url">URL</Label>
                    <Input
                      id="link-url"
                      value={linkUrl}
                      onChange={(e) => setLinkUrl(e.target.value)}
                      placeholder="https://example.com"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button onClick={insertLink}>Insert Link</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Image className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Insert Image</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="image-url">Image URL</Label>
                    <Input
                      id="image-url"
                      value={imageUrl}
                      onChange={(e) => setImageUrl(e.target.value)}
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>
                  <div>
                    <Label htmlFor="image-alt">Alt Text</Label>
                    <Input
                      id="image-alt"
                      value={imageAlt}
                      onChange={(e) => setImageAlt(e.target.value)}
                      placeholder="Image description"
                    />
                  </div>
                  <div className="text-center">
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Image
                    </Button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button onClick={insertImage}>Insert Image</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <div className="w-px h-6 bg-border mx-1" />
            
            <Select onValueChange={(value) => insertPlaceholder(value)}>
              <SelectTrigger className="w-32 h-8">
                <SelectValue placeholder="Variables" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="customerName">Customer Name</SelectItem>
                <SelectItem value="orderNumber">Order Number</SelectItem>
                <SelectItem value="username">Username</SelectItem>
                <SelectItem value="password">Password</SelectItem>
                <SelectItem value="productName">Product Name</SelectItem>
                <SelectItem value="amount">Amount</SelectItem>
                <SelectItem value="country">Country</SelectItem>
                <SelectItem value="appType">App Type</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Visual Editor */}
          <div
            ref={editorRef}
            className="min-h-[400px] p-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            contentEditable
            dangerouslySetInnerHTML={{ __html: htmlContent }}
            onInput={(e) => {
              const newContent = (e.target as HTMLDivElement).innerHTML;
              handleHtmlChange(newContent);
            }}
            style={{ minHeight: `${height}px` }}
          />
        </TabsContent>

        <TabsContent value="code">
          <Textarea
            value={htmlContent}
            onChange={(e) => handleHtmlChange(e.target.value)}
            className="font-mono text-sm"
            style={{ minHeight: `${height}px` }}
            placeholder="Enter HTML code here..."
          />
        </TabsContent>

        {showPreview && (
          <TabsContent value="preview">
            <div className="border rounded-md overflow-hidden">
              <div className="bg-muted p-2 text-sm text-muted-foreground text-center">
                Preview Mode: {previewMode.charAt(0).toUpperCase() + previewMode.slice(1)}
              </div>
              <div className="p-4 bg-gray-100 flex justify-center">
                <div
                  style={{ width: getPreviewWidth(), maxWidth: '100%' }}
                  className="bg-white shadow-lg"
                >
                  <iframe
                    srcDoc={htmlContent}
                    className="w-full border-0"
                    style={{ minHeight: `${height}px` }}
                    title="Email Preview"
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        )}

        <TabsContent value="templates">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {emailTemplates.map((template) => (
              <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <p className="text-sm text-muted-foreground">{template.description}</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="border rounded p-2 bg-muted/50 h-32 overflow-hidden">
                      <div
                        className="text-xs"
                        dangerouslySetInnerHTML={{ __html: template.html }}
                        style={{ transform: 'scale(0.3)', transformOrigin: 'top left', width: '333%', height: '333%' }}
                      />
                    </div>
                    <Button
                      onClick={() => loadTemplate(template)}
                      className="w-full"
                      variant="outline"
                    >
                      Use Template
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedEmailEditor;
